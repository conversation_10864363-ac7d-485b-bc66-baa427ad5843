const { Product } = require('../models');
const { Op } = require('sequelize');

// Helper function for error response
const sendErrorResponse = (res, statusCode, message, error = null) => {
  const response = {
    success: false,
    message,
    timestamp: new Date().toISOString()
  };
  
  if (error && process.env.NODE_ENV === 'development') {
    response.error = error.message;
  }
  
  return res.status(statusCode).json(response);
};

// Helper function for success response
const sendSuccessResponse = (res, statusCode, message, data = null) => {
  const response = {
    success: true,
    message,
    timestamp: new Date().toISOString()
  };
  
  if (data !== null) {
    response.data = data;
  }
  
  return res.status(statusCode).json(response);
};

// Get all products
exports.getAllProducts = async (req, res) => {
  try {
    const { page = 1, limit = 10, search, supplier_id, low_stock } = req.query;
    const offset = (page - 1) * limit;
    
    // Build where clause
    const whereClause = {};
    
    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.iLike]: `%${search}%` } },
        { code: { [Op.iLike]: `%${search}%` } },
        { description: { [Op.iLike]: `%${search}%` } }
      ];
    }
    
    if (supplier_id) {
      whereClause.supplier_id = supplier_id;
    }
    
    if (low_stock) {
      whereClause.quantity = { [Op.lte]: parseInt(low_stock) };
    }
    
    const { count, rows } = await Product.findAndCountAll({
      where: whereClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['created_at', 'DESC']]
    });
    
    const totalPages = Math.ceil(count / limit);
    
    sendSuccessResponse(res, 200, 'تم جلب المنتجات بنجاح', {
      products: rows,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalItems: count,
        itemsPerPage: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Error in getAllProducts:', error);
    sendErrorResponse(res, 500, 'خطأ في جلب المنتجات', error);
  }
};

// Get single product by ID
exports.getProductById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const product = await Product.findByPk(id);
    
    if (!product) {
      return sendErrorResponse(res, 404, 'المنتج غير موجود');
    }
    
    sendSuccessResponse(res, 200, 'تم جلب المنتج بنجاح', { product });
  } catch (error) {
    console.error('Error in getProductById:', error);
    sendErrorResponse(res, 500, 'خطأ في جلب المنتج', error);
  }
};

// Create new product
exports.createProduct = async (req, res) => {
  try {
    const { name, code, unit, quantity, description, supplier_id } = req.body;
    
    // Validation
    if (!name || !code) {
      return sendErrorResponse(res, 400, 'اسم المنتج والكود مطلوبان');
    }
    
    if (quantity !== undefined && quantity < 0) {
      return sendErrorResponse(res, 400, 'الكمية يجب أن تكون أكبر من أو تساوي 0');
    }
    
    const product = await Product.create({
      name,
      code,
      unit: unit || 'قطعة',
      quantity: quantity || 0,
      description,
      supplier_id
    });
    
    sendSuccessResponse(res, 201, 'تم إنشاء المنتج بنجاح', { product });
  } catch (error) {
    console.error('Error in createProduct:', error);
    
    // Handle unique constraint error
    if (error.name === 'SequelizeUniqueConstraintError') {
      return sendErrorResponse(res, 400, 'كود المنتج موجود مسبقاً');
    }
    
    // Handle validation errors
    if (error.name === 'SequelizeValidationError') {
      const validationErrors = error.errors.map(err => err.message);
      return sendErrorResponse(res, 400, validationErrors.join(', '));
    }
    
    sendErrorResponse(res, 500, 'خطأ في إنشاء المنتج', error);
  }
};

// Update product
exports.updateProduct = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, code, unit, quantity, description, supplier_id } = req.body;

    const product = await Product.findByPk(id);

    if (!product) {
      return sendErrorResponse(res, 404, 'المنتج غير موجود');
    }

    // Validation
    if (quantity !== undefined && quantity < 0) {
      return sendErrorResponse(res, 400, 'الكمية يجب أن تكون أكبر من أو تساوي 0');
    }

    // Update product
    await product.update({
      name: name || product.name,
      code: code || product.code,
      unit: unit || product.unit,
      quantity: quantity !== undefined ? quantity : product.quantity,
      description: description !== undefined ? description : product.description,
      supplier_id: supplier_id !== undefined ? supplier_id : product.supplier_id
    });

    sendSuccessResponse(res, 200, 'تم تحديث المنتج بنجاح', { product });
  } catch (error) {
    console.error('Error in updateProduct:', error);

    // Handle unique constraint error
    if (error.name === 'SequelizeUniqueConstraintError') {
      return sendErrorResponse(res, 400, 'كود المنتج موجود مسبقاً');
    }

    // Handle validation errors
    if (error.name === 'SequelizeValidationError') {
      const validationErrors = error.errors.map(err => err.message);
      return sendErrorResponse(res, 400, validationErrors.join(', '));
    }

    sendErrorResponse(res, 500, 'خطأ في تحديث المنتج', error);
  }
};

// Delete product
exports.deleteProduct = async (req, res) => {
  try {
    const { id } = req.params;

    const product = await Product.findByPk(id);

    if (!product) {
      return sendErrorResponse(res, 404, 'المنتج غير موجود');
    }

    await product.destroy();

    sendSuccessResponse(res, 200, 'تم حذف المنتج بنجاح');
  } catch (error) {
    console.error('Error in deleteProduct:', error);
    sendErrorResponse(res, 500, 'خطأ في حذف المنتج', error);
  }
};

// Get products statistics
exports.getProductsStats = async (req, res) => {
  try {
    const totalProducts = await Product.count();
    const inStockProducts = await Product.count({
      where: {
        quantity: { [Op.gt]: 0 }
      }
    });
    const outOfStockProducts = await Product.count({
      where: {
        quantity: 0
      }
    });
    const lowStockProducts = await Product.count({
      where: {
        quantity: { [Op.between]: [1, 10] }
      }
    });

    const stats = {
      totalProducts,
      inStockProducts,
      outOfStockProducts,
      lowStockProducts
    };

    sendSuccessResponse(res, 200, 'تم جلب إحصائيات المنتجات بنجاح', { stats });
  } catch (error) {
    console.error('Error in getProductsStats:', error);
    sendErrorResponse(res, 500, 'خطأ في جلب إحصائيات المنتجات', error);
  }
};
