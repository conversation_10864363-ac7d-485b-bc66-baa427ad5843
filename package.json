{"name": "product-management-module", "version": "1.0.0", "description": "Product Management Module with Node.js, Express, Sequelize and PostgreSQL", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "migrate": "npx sequelize-cli db:migrate", "migrate:undo": "npx sequelize-cli db:migrate:undo", "seed": "npx sequelize-cli db:seed:all", "seed:undo": "npx sequelize-cli db:seed:undo:all"}, "keywords": ["nodejs", "express", "sequelize", "postgresql", "product-management"], "author": "", "license": "ISC", "dependencies": {"express": "^4.18.2", "sequelize": "^6.35.2", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "dotenv": "^16.3.1", "cors": "^2.8.5", "body-parser": "^1.20.2"}, "devDependencies": {"nodemon": "^3.0.2", "sequelize-cli": "^6.6.2"}}