# دليل البدء السريع - وحدة إدارة المنتجات

## خطوات التشغيل السريع

### 1. تثبيت التبعيات
```bash
npm install
```

### 2. إعد<PERSON> قاعدة البيانات
1. إنشاء قاعدة بيانات PostgreSQL:
```sql
CREATE DATABASE product_management_db;
```

2. تحديث ملف `.env`:
```env
DB_HOST=localhost
DB_PORT=5432
DB_NAME=product_management_db
DB_USER=postgres
DB_PASSWORD=your_password_here
PORT=3000
NODE_ENV=development
```

### 3. تشغيل الهجرات والبذور
```bash
# تشغيل الهجرات
npm run migrate

# إضافة بيانات تجريبية (اختياري)
npm run seed
```

### 4. تشغيل التطبيق
```bash
# للتطوير
npm run dev

# أو للإنتاج
npm start
```

### 5. اختبار API

افتح المتصفح على: `http://localhost:3000`

أو استخدم الأوامر التالية:

```bash
# فحص صحة الخادم
curl http://localhost:3000/health

# جلب كل المنتجات
curl http://localhost:3000/api/products

# إضافة منتج جديد
curl -X POST http://localhost:3000/api/products \
  -H "Content-Type: application/json" \
  -d '{
    "name": "منتج تجريبي",
    "code": "TEST-001",
    "unit": "قطعة",
    "quantity": 10,
    "description": "هذا منتج تجريبي"
  }'
```

## الأوامر المفيدة

```bash
# تثبيت التبعيات
npm install

# تشغيل التطبيق للتطوير (مع إعادة التشغيل التلقائي)
npm run dev

# تشغيل التطبيق للإنتاج
npm start

# تشغيل الهجرات
npm run migrate

# التراجع عن الهجرة الأخيرة
npm run migrate:undo

# إضافة البيانات التجريبية
npm run seed

# حذف البيانات التجريبية
npm run seed:undo
```

## اختبار مع Postman

1. استورد ملف `postman-examples.json` في Postman
2. تأكد من أن المتغير `baseUrl` مضبوط على `http://localhost:3000`
3. جرب الطلبات المختلفة

## المسارات الأساسية

- **الصفحة الرئيسية**: `http://localhost:3000/`
- **فحص الصحة**: `http://localhost:3000/health`
- **API المنتجات**: `http://localhost:3000/api/products`
- **إحصائيات المنتجات**: `http://localhost:3000/api/products/stats`

## استكشاف الأخطاء

### خطأ في الاتصال بقاعدة البيانات
- تأكد من تشغيل PostgreSQL
- تحقق من معلومات الاتصال في ملف `.env`
- تأكد من وجود قاعدة البيانات

### خطأ في المنفذ
- تأكد من أن المنفذ 3000 غير مستخدم
- أو غير المنفذ في ملف `.env`

### خطأ في الهجرات
```bash
# إعادة تشغيل الهجرات
npm run migrate:undo
npm run migrate
```

## الميزات المتاحة

✅ **CRUD كامل للمنتجات**
- إضافة منتج جديد
- عرض كل المنتجات
- عرض منتج واحد
- تحديث منتج
- حذف منتج

✅ **البحث والتصفية**
- البحث في الاسم والكود والوصف
- تصفية حسب المورد
- عرض المنتجات قليلة المخزون

✅ **التصفح والإحصائيات**
- تصفح النتائج (Pagination)
- إحصائيات شاملة للمنتجات

✅ **التحقق من البيانات**
- التحقق من الحقول المطلوبة
- التحقق من فرادة الكود
- التحقق من صحة الكمية

✅ **معالجة الأخطاء**
- رسائل خطأ واضحة باللغة العربية
- تسجيل مفصل للعمليات
