{"info": {"name": "Product Management API", "description": "مجموعة أمثلة لاختبار API إدارة المنتجات", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}}}, {"name": "Get All Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/products", "host": ["{{baseUrl}}"], "path": ["api", "products"]}}}, {"name": "Get Products with Pagination", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/products?page=1&limit=5", "host": ["{{baseUrl}}"], "path": ["api", "products"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "5"}]}}}, {"name": "Search Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/products?search=لابتوب", "host": ["{{baseUrl}}"], "path": ["api", "products"], "query": [{"key": "search", "value": "لابتوب"}]}}}, {"name": "Get Low Stock Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/products?low_stock=10", "host": ["{{baseUrl}}"], "path": ["api", "products"], "query": [{"key": "low_stock", "value": "10"}]}}}, {"name": "Get Product by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/products/1", "host": ["{{baseUrl}}"], "path": ["api", "products", "1"]}}}, {"name": "Create Product - Laptop", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"لابتوب ديل انسبايرون\",\n  \"code\": \"DELL-INS-001\",\n  \"unit\": \"قطعة\",\n  \"quantity\": 25,\n  \"description\": \"لابتوب ديل انسبايرون 15 بوصة، معالج Intel Core i5، ذاكرة 8GB RAM، قرص صلب 512GB SSD\",\n  \"supplier_id\": null\n}"}, "url": {"raw": "{{baseUrl}}/api/products", "host": ["{{baseUrl}}"], "path": ["api", "products"]}}}, {"name": "Create Product - Mouse", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"ماوس لوجيتك\",\n  \"code\": \"LOG-MOU-001\",\n  \"unit\": \"قطعة\",\n  \"quantity\": 100,\n  \"description\": \"ماوس لوجيتك لاسلكي، دقة عالية، بطارية طويلة المدى\",\n  \"supplier_id\": null\n}"}, "url": {"raw": "{{baseUrl}}/api/products", "host": ["{{baseUrl}}"], "path": ["api", "products"]}}}, {"name": "Create Product - Keyboard", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"كيبورد ميكانيكي\",\n  \"code\": \"MECH-KEY-001\",\n  \"unit\": \"قطعة\",\n  \"quantity\": 5,\n  \"description\": \"كيبورد ميكانيكي للألعاب، إضاءة RGB، مفاتيح Cherry MX\",\n  \"supplier_id\": null\n}"}, "url": {"raw": "{{baseUrl}}/api/products", "host": ["{{baseUrl}}"], "path": ["api", "products"]}}}, {"name": "Update Product", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"لابتوب ديل انسبايرون محدث\",\n  \"quantity\": 20,\n  \"description\": \"لابتوب ديل انسبايرون 15 بوصة، معالج Intel Core i7، ذاكرة 16GB RAM، قرص صلب 1TB SSD - محدث\"\n}"}, "url": {"raw": "{{baseUrl}}/api/products/1", "host": ["{{baseUrl}}"], "path": ["api", "products", "1"]}}}, {"name": "Delete Product", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/products/1", "host": ["{{baseUrl}}"], "path": ["api", "products", "1"]}}}, {"name": "Get Products Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/products/stats", "host": ["{{baseUrl}}"], "path": ["api", "products", "stats"]}}}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}]}