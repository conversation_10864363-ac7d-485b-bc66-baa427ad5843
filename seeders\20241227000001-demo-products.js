'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.bulkInsert('products', [
      {
        name: 'لابتوب ديل انسبايرون 15',
        code: 'DELL-INS-15-001',
        unit: 'قطعة',
        quantity: 25,
        description: 'لابتوب ديل انسبايرون 15 بوصة، معالج Intel Core i5، ذاكرة 8GB RAM، قرص صلب 512GB SSD',
        supplier_id: null,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: 'ماوس لوجيتك MX Master 3',
        code: 'LOG-MX3-001',
        unit: 'قطعة',
        quantity: 50,
        description: 'ماوس لوجيتك MX Master 3 لاسلكي، دقة عالية، بطارية طويلة المدى',
        supplier_id: null,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: 'كيبورد ميكانيكي للألعاب',
        code: 'MECH-GAM-001',
        unit: 'قطعة',
        quantity: 8,
        description: 'كيبورد ميكانيكي للألعاب، إضاءة RGB، مفاتيح Cherry MX Blue',
        supplier_id: null,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: 'شاشة سامسونج 24 بوصة',
        code: 'SAM-MON-24-001',
        unit: 'قطعة',
        quantity: 15,
        description: 'شاشة سامسونج 24 بوصة، دقة Full HD، تقنية IPS',
        supplier_id: null,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: 'سماعات سوني WH-1000XM4',
        code: 'SONY-WH1000XM4-001',
        unit: 'قطعة',
        quantity: 12,
        description: 'سماعات سوني لاسلكية مع تقنية إلغاء الضوضاء النشطة',
        supplier_id: null,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: 'هارد ديسك خارجي 1TB',
        code: 'EXT-HDD-1TB-001',
        unit: 'قطعة',
        quantity: 30,
        description: 'هارد ديسك خارجي محمول بسعة 1 تيرابايت، USB 3.0',
        supplier_id: null,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: 'كاميرا ويب لوجيتك C920',
        code: 'LOG-C920-001',
        unit: 'قطعة',
        quantity: 20,
        description: 'كاميرا ويب لوجيتك C920، دقة Full HD، مايكروفون مدمج',
        supplier_id: null,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: 'طابعة HP LaserJet',
        code: 'HP-LJ-001',
        unit: 'قطعة',
        quantity: 5,
        description: 'طابعة HP LaserJet أبيض وأسود، سرعة طباعة عالية',
        supplier_id: null,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: 'راوتر TP-Link AC1750',
        code: 'TPL-AC1750-001',
        unit: 'قطعة',
        quantity: 18,
        description: 'راوتر TP-Link AC1750 ثنائي النطاق، سرعة عالية',
        supplier_id: null,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: 'بطارية محمولة 20000mAh',
        code: 'PWR-BANK-20K-001',
        unit: 'قطعة',
        quantity: 35,
        description: 'بطارية محمولة بسعة 20000 مللي أمبير، شحن سريع، منافذ متعددة',
        supplier_id: null,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: 'كابل USB-C',
        code: 'CABLE-USBC-001',
        unit: 'قطعة',
        quantity: 100,
        description: 'كابل USB-C عالي الجودة، طول 2 متر، شحن وبيانات',
        supplier_id: null,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: 'حامل لابتوب قابل للتعديل',
        code: 'LAP-STAND-001',
        unit: 'قطعة',
        quantity: 22,
        description: 'حامل لابتوب قابل للتعديل، مصنوع من الألومنيوم، تهوية ممتازة',
        supplier_id: null,
        created_at: new Date(),
        updated_at: new Date()
      }
    ], {});
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('products', null, {});
  }
};
