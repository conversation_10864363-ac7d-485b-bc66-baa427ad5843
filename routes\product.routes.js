const express = require('express');
const router = express.Router();
const productController = require('../controllers/product.controller');

// Middleware for request logging
const requestLogger = (req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.originalUrl}`);
  next();
};

// Apply logging middleware to all routes
router.use(requestLogger);

// Validation middleware for product creation/update
const validateProductData = (req, res, next) => {
  const { name, code, quantity } = req.body;
  
  // Check required fields for POST requests
  if (req.method === 'POST') {
    if (!name || !code) {
      return res.status(400).json({
        success: false,
        message: 'اسم المنتج والكود مطلوبان',
        timestamp: new Date().toISOString()
      });
    }
  }
  
  // Validate quantity if provided
  if (quantity !== undefined) {
    if (typeof quantity !== 'number' || quantity < 0) {
      return res.status(400).json({
        success: false,
        message: 'الكمية يجب أن تكون رقم صحيح أكبر من أو يساوي 0',
        timestamp: new Date().toISOString()
      });
    }
  }
  
  next();
};

// Validation middleware for ID parameter
const validateIdParam = (req, res, next) => {
  const { id } = req.params;
  
  if (!id || isNaN(parseInt(id))) {
    return res.status(400).json({
      success: false,
      message: 'معرف المنتج يجب أن يكون رقم صحيح',
      timestamp: new Date().toISOString()
    });
  }
  
  next();
};

// Routes

/**
 * @route   GET /api/products
 * @desc    Get all products with optional filtering and pagination
 * @access  Public
 * @params  ?page=1&limit=10&search=keyword&supplier_id=1&low_stock=10
 */
router.get('/', productController.getAllProducts);

/**
 * @route   GET /api/products/stats
 * @desc    Get products statistics
 * @access  Public
 */
router.get('/stats', productController.getProductsStats);

/**
 * @route   GET /api/products/:id
 * @desc    Get single product by ID
 * @access  Public
 */
router.get('/:id', validateIdParam, productController.getProductById);

/**
 * @route   POST /api/products
 * @desc    Create new product
 * @access  Public
 * @body    { name, code, unit?, quantity?, description?, supplier_id? }
 */
router.post('/', validateProductData, productController.createProduct);

/**
 * @route   PUT /api/products/:id
 * @desc    Update product by ID
 * @access  Public
 * @body    { name?, code?, unit?, quantity?, description?, supplier_id? }
 */
router.put('/:id', validateIdParam, validateProductData, productController.updateProduct);

/**
 * @route   DELETE /api/products/:id
 * @desc    Delete product by ID
 * @access  Public
 */
router.delete('/:id', validateIdParam, productController.deleteProduct);

module.exports = router;
